import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase/server';

// GET /api/events/categories/[id] - Get single category
export async function GET(
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
) {
    try {
        const supabase = await createClient();
        const { id } = await params;

        const { data: category, error } = await supabase
            .from('event_categories')
            .select('*')
            .eq('id', id)
            .single();

        if (error) {
            console.error('Error fetching category:', error);
            return NextResponse.json(
                { error: 'Category not found' },
                { status: 404 }
            );
        }

        // Get event count for this category
        const { count } = await supabase
            .from('events')
            .select('*', { count: 'exact', head: true })
            .eq('category_id', id)
            .eq('is_active', true)
            .not('published_at', 'is', null);

        return NextResponse.json({
            category: {
                ...category,
                event_count: count || 0,
            },
        });

    } catch (error) {
        console.error('API Error:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}

// PUT /api/events/categories/[id] - Update category
export async function PUT(
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
) {
    try {
        const supabase = await createClient();

        // Check authentication - TEMPORARILY DISABLED FOR TESTING
        // const { data: { user }, error: authError } = await supabase.auth.getUser();
        // if (authError || !user) {
        //     return NextResponse.json(
        //         { error: 'Unauthorized' },
        //         { status: 401 }
        //     );
        // }

        const { id } = await params;
        const updateData = await request.json();

        // Remove fields that shouldn't be updated directly
        const { id: _, created_at, updated_at, created_by, ...allowedUpdates } = updateData;

        // If slug is being updated, check for conflicts
        if (allowedUpdates.slug) {
            const { data: existingCategory } = await supabase
                .from('event_categories')
                .select('id')
                .eq('slug', allowedUpdates.slug)
                .neq('id', id)
                .single();

            if (existingCategory) {
                return NextResponse.json(
                    { error: 'A category with this slug already exists' },
                    { status: 400 }
                );
            }
        }

        // Update category
        const { data: category, error } = await supabase
            .from('event_categories')
            .update({
                ...allowedUpdates,
                updated_by: null,
            })
            .eq('id', id)
            .select()
            .single();

        if (error) {
            console.error('Error updating category:', error);
            return NextResponse.json(
                { error: 'Failed to update category' },
                { status: 500 }
            );
        }

        return NextResponse.json({
            success: true,
            category,
        });

    } catch (error) {
        console.error('API Error:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}

// DELETE /api/events/categories/[id] - Delete category
export async function DELETE(
    request: NextRequest,
    { params }: { params: Promise<{ id: string }> }
) {
    try {
        const supabase = await createClient();

        // Check authentication - TEMPORARILY DISABLED FOR TESTING
        // const { data: { user }, error: authError } = await supabase.auth.getUser();
        // if (authError || !user) {
        //     return NextResponse.json(
        //         { error: 'Unauthorized' },
        //         { status: 401 }
        //     );
        // }

        const { id } = await params;

        // Check if category has events
        const { count } = await supabase
            .from('events')
            .select('*', { count: 'exact', head: true })
            .eq('category_id', id);

        if (count && count > 0) {
            return NextResponse.json(
                { error: `Cannot delete category. It has ${count} event(s) associated with it.` },
                { status: 400 }
            );
        }

        // Delete category
        const { error } = await supabase
            .from('event_categories')
            .delete()
            .eq('id', id);

        if (error) {
            console.error('Error deleting category:', error);
            return NextResponse.json(
                { error: 'Failed to delete category' },
                { status: 500 }
            );
        }

        return NextResponse.json({
            success: true,
            message: 'Category deleted successfully',
        });

    } catch (error) {
        console.error('API Error:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}
