"use client";

import React, { useEffect, useState } from "react";
import { Toaster } from "@/components/ui/sonner";
import AdminSidebar from "./components/admin-sidebar";
import { useRouter } from "next/navigation";
import { createClient } from "@/lib/supabase/client";
import { User as SupabaseUser } from "@supabase/supabase-js";

export default function AdminLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    const [isLoading, setIsLoading] = useState(true);
    const [user, setUser] = useState<SupabaseUser | null>(null);
    const [isAuthenticated, setIsAuthenticated] = useState(false);
    const router = useRouter();
    const supabase = createClient();

    // Check authentication status
    useEffect(() => {
        console.log("checkAuth");
        const checkAuth = async () => {
            try {
                const {
                    data: { user },
                    error,
                } = await supabase.auth.getUser();

                if (error || !user) {
                    router.push("/login");
                } else {
                    setUser(user);
                    setIsAuthenticated(true);
                }
            } catch (error) {
                console.error("Error checking auth status:", error);
                router.push("/login");
            } finally {
                setIsLoading(false);
            }
        };

        checkAuth();
    }, [router, supabase.auth]);

    // Show loading state while checking authentication
    if (isLoading || !isAuthenticated) {
        return (
            <div className="flex h-screen items-center justify-center bg-gray-100">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#a5cd39] mx-auto"></div>
                    <p className="mt-4 text-gray-600">Loading...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="flex h-screen bg-gray-100">
            {/* Sidebar */}
            <AdminSidebar user={user} isSignedIn={isAuthenticated} />

            {/* Main Content */}
            <div className="flex-1 overflow-auto">
                <main className="p-6">{children}</main>
            </div>

            <Toaster />
        </div>
    );
}
